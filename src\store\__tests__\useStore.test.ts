import { renderHook, act } from '@testing-library/react';

// Import the actual store for testing (not mocked)
jest.unmock('../useStore');
import { create } from 'zustand';

// Create a test store instance for each test
const createTestStore = () => create<any>()((set) => ({
  theme: 'dark',
  isMobileMenuOpen: false,
  isLoading: false,
  prefersReducedMotion: false,
  contactFormData: {
    name: '',
    email: '',
    message: '',
    interest: '',
  },
  setTheme: (theme: string) => set({ theme }),
  setMobileMenuOpen: (open: boolean) => set({ isMobileMenuOpen: open }),
  setContactFormData: (data: any) =>
    set((state: any) => ({
      contactFormData: { ...state.contactFormData, ...data },
    })),
  resetContactForm: () => set({
    contactFormData: {
      name: '',
      email: '',
      message: '',
      interest: '',
    },
  }),
  setPrefersReducedMotion: (value: boolean) => set({ prefersReducedMotion: value }),
  setLoading: (loading: boolean) => set({ isLoading: loading }),
}));

describe('useStore', () => {
  let testStore: any;

  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
    // Create a fresh store instance for each test
    testStore = createTestStore();
  });

  it('initializes with default values', () => {
    const { result } = renderHook(() => testStore());

    expect(result.current.theme).toBe('dark');
    expect(result.current.isMobileMenuOpen).toBe(false);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.prefersReducedMotion).toBe(false);
    expect(result.current.contactFormData).toEqual({
      name: '',
      email: '',
      message: '',
      interest: '',
    });
  });

  it('updates theme correctly', () => {
    const { result } = renderHook(() => testStore());

    act(() => {
      result.current.setTheme('light');
    });

    expect(result.current.theme).toBe('light');
  });

  it('toggles mobile menu correctly', () => {
    const { result } = renderHook(() => testStore());

    act(() => {
      result.current.setMobileMenuOpen(true);
    });

    expect(result.current.isMobileMenuOpen).toBe(true);

    act(() => {
      result.current.setMobileMenuOpen(false);
    });

    expect(result.current.isMobileMenuOpen).toBe(false);
  });

  it('updates contact form data correctly', () => {
    const { result } = renderHook(() => testStore());

    act(() => {
      result.current.setContactFormData({
        name: 'John Doe',
        email: '<EMAIL>',
      });
    });

    expect(result.current.contactFormData.name).toBe('John Doe');
    expect(result.current.contactFormData.email).toBe('<EMAIL>');
    expect(result.current.contactFormData.message).toBe(''); // Should preserve other fields
  });

  it('resets contact form correctly', () => {
    const { result } = renderHook(() => testStore());

    // First set some data
    act(() => {
      result.current.setContactFormData({
        name: 'John Doe',
        email: '<EMAIL>',
        message: 'Test message',
        interest: 'AI Platform',
      });
    });

    // Then reset
    act(() => {
      result.current.resetContactForm();
    });

    expect(result.current.contactFormData).toEqual({
      name: '',
      email: '',
      message: '',
      interest: '',
    });
  });
});

describe('Store selectors', () => {
  let testStore: any;

  beforeEach(() => {
    localStorage.clear();
    testStore = createTestStore();
  });

  it('contact form selector returns data and functions', () => {
    const { result } = renderHook(() => {
      const store = testStore();
      return {
        data: store.contactFormData,
        setData: store.setContactFormData,
        reset: store.resetContactForm,
      };
    });

    expect(result.current.data).toEqual({
      name: '',
      email: '',
      message: '',
      interest: '',
    });
    expect(typeof result.current.setData).toBe('function');
    expect(typeof result.current.reset).toBe('function');
  });

  it('theme selector returns current theme', () => {
    const { result } = renderHook(() => testStore().theme);

    expect(result.current).toBe('dark');
  });
});

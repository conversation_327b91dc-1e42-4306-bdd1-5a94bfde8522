import React, { Profiler, ProfilerOnRenderCallback, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// React Profiler interaction type
interface Interaction {
  id: number;
  name: string;
  timestamp: number;
}

interface PerformanceMetrics {
  id: string;
  phase: 'mount' | 'update';
  actualDuration: number;
  baseDuration: number;
  startTime: number;
  commitTime: number;
  interactions: Set<Interaction>;
}

interface PerformanceMonitorProps {
  children: React.ReactNode;
  id: string;
  showMetrics?: boolean;
}

/**
 * Performance monitoring component using React 19's Profiler API
 */
export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  children,
  id,
  showMetrics = process.env.NODE_ENV === 'development',
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const lastUpdateRef = React.useRef<number>(0);

  const onRenderCallback: ProfilerOnRenderCallback = (
    id,
    phase,
    actualDuration,
    baseDuration,
    startTime,
    commitTime,
    interactions
  ) => {
    const metric: PerformanceMetrics = {
      id,
      phase,
      actualDuration,
      baseDuration,
      startTime,
      commitTime,
      interactions,
    };

    // Throttle updates to prevent infinite loops
    const now = performance.now();
    if (now - lastUpdateRef.current < 100) { // Throttle to max 10 updates per second
      return;
    }
    lastUpdateRef.current = now;

    // Use requestIdleCallback to avoid blocking the main thread
    if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        setMetrics(prev => [...prev.slice(-9), metric]); // Keep last 10 metrics
      });
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(() => {
        setMetrics(prev => [...prev.slice(-9), metric]);
      }, 0);
    }

    // Log performance warnings in development (don't use state here)
    if (process.env.NODE_ENV === 'development') {
      if (actualDuration > 16) { // More than one frame (16ms)
        console.warn(`Slow render detected in ${id}:`, {
          phase,
          actualDuration: `${actualDuration.toFixed(2)}ms`,
          baseDuration: `${baseDuration.toFixed(2)}ms`,
        });
      }
    }
  };

  // Web Vitals monitoring
  useEffect(() => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      // Monitor Largest Contentful Paint (LCP)
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'largest-contentful-paint') {
            console.log('LCP:', entry.startTime);
          }
        });
      });

      try {
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
      } catch (e) {
        // Fallback for browsers that don't support this
      }

      return () => observer.disconnect();
    }
  }, []);

  const averageRenderTime = metrics.length > 0 
    ? metrics.reduce((sum, m) => sum + m.actualDuration, 0) / metrics.length 
    : 0;

  const slowRenders = metrics.filter(m => m.actualDuration > 16).length;

  if (!showMetrics) {
    return (
      <Profiler id={id} onRender={onRenderCallback}>
        {children}
      </Profiler>
    );
  }

  return (
    <div className="relative">
      <Profiler id={id} onRender={onRenderCallback}>
        {children}
      </Profiler>

      {/* Performance Metrics Overlay */}
      <div className="fixed top-4 right-4 z-50">
        <motion.button
          onClick={() => setIsVisible(!isVisible)}
          className="bg-black/80 text-white px-3 py-2 rounded-lg text-xs font-mono border border-gray-600 hover:bg-black/90 transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          ⚡ {id}
        </motion.button>

        <AnimatePresence>
          {isVisible && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              className="absolute top-12 right-0 bg-black/90 text-white p-4 rounded-lg border border-gray-600 min-w-[300px] font-mono text-xs"
            >
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Component:</span>
                  <span className="text-blue-400">{id}</span>
                </div>
                <div className="flex justify-between">
                  <span>Avg Render:</span>
                  <span className={averageRenderTime > 16 ? 'text-red-400' : 'text-green-400'}>
                    {averageRenderTime.toFixed(2)}ms
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Slow Renders:</span>
                  <span className={slowRenders > 0 ? 'text-red-400' : 'text-green-400'}>
                    {slowRenders}/{metrics.length}
                  </span>
                </div>
                
                {metrics.length > 0 && (
                  <div className="mt-3 pt-2 border-t border-gray-600">
                    <div className="text-gray-400 mb-1">Recent Renders:</div>
                    <div className="space-y-1 max-h-32 overflow-y-auto">
                      {metrics.slice(-5).reverse().map((metric, index) => (
                        <div key={index} className="flex justify-between text-xs">
                          <span className={metric.phase === 'mount' ? 'text-blue-400' : 'text-yellow-400'}>
                            {metric.phase}
                          </span>
                          <span className={metric.actualDuration > 16 ? 'text-red-400' : 'text-green-400'}>
                            {metric.actualDuration.toFixed(2)}ms
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

/**
 * Hook for measuring custom performance metrics
 */
export const usePerformanceMetrics = (componentName: string) => {
  const [startTime, setStartTime] = useState<number | null>(null);

  const startMeasure = () => {
    setStartTime(performance.now());
  };

  const endMeasure = (operationName: string) => {
    if (startTime) {
      const duration = performance.now() - startTime;
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`${componentName} - ${operationName}:`, `${duration.toFixed(2)}ms`);
        
        if (duration > 100) {
          console.warn(`Slow operation detected in ${componentName}:`, {
            operation: operationName,
            duration: `${duration.toFixed(2)}ms`,
          });
        }
      }

      setStartTime(null);
      return duration;
    }
    return 0;
  };

  return { startMeasure, endMeasure };
};

/**
 * Higher-order component for automatic performance monitoring
 */
export const withPerformanceMonitoring = <P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
) => {
  const WrappedComponent = (props: P) => (
    <PerformanceMonitor id={componentName}>
      <Component {...props} />
    </PerformanceMonitor>
  );

  WrappedComponent.displayName = `withPerformanceMonitoring(${componentName})`;
  return WrappedComponent;
};

export default PerformanceMonitor;

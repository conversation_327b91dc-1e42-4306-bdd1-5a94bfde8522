import React, { Suspense, ReactNode } from 'react';
import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';

interface SuspenseBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  className?: string;
}

/**
 * Loading component with smooth animations
 */
const DefaultFallback = () => (
  <div className="flex items-center justify-center min-h-[200px] w-full">
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className="flex flex-col items-center space-y-4"
    >
      <motion.div
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
      >
        <Loader2 className="h-8 w-8 text-primary" />
      </motion.div>
      <motion.p
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="text-sm text-gray-400"
      >
        Loading...
      </motion.p>
    </motion.div>
  </div>
);

/**
 * Skeleton loader for content
 */
export const SkeletonLoader = ({ className = "" }: { className?: string }) => (
  <div className={`animate-pulse ${className}`}>
    <div className="space-y-4">
      <div className="h-4 bg-gray-700 rounded w-3/4"></div>
      <div className="h-4 bg-gray-700 rounded w-1/2"></div>
      <div className="h-4 bg-gray-700 rounded w-5/6"></div>
    </div>
  </div>
);

/**
 * Card skeleton for project cards
 */
export const CardSkeleton = () => (
  <div className="animate-pulse">
    <div className="bg-gray-800 rounded-lg p-6 space-y-4">
      <div className="flex justify-between items-start">
        <div className="h-6 bg-gray-700 rounded w-20"></div>
        <div className="h-4 bg-gray-700 rounded w-8"></div>
      </div>
      <div className="h-6 bg-gray-700 rounded w-3/4"></div>
      <div className="space-y-2">
        <div className="h-4 bg-gray-700 rounded w-full"></div>
        <div className="h-4 bg-gray-700 rounded w-2/3"></div>
      </div>
      <div className="flex gap-2">
        <div className="h-6 bg-gray-700 rounded w-16"></div>
        <div className="h-6 bg-gray-700 rounded w-20"></div>
        <div className="h-6 bg-gray-700 rounded w-14"></div>
      </div>
      <div className="h-10 bg-gray-700 rounded w-full"></div>
    </div>
  </div>
);

/**
 * Enhanced Suspense boundary with error handling and smooth transitions
 */
export const SuspenseBoundary: React.FC<SuspenseBoundaryProps> = ({
  children,
  fallback = <DefaultFallback />,
  className = "",
}) => {
  return (
    <div className={className}>
      <Suspense fallback={fallback}>
        {children}
      </Suspense>
    </div>
  );
};

/**
 * Suspense boundary specifically for page transitions
 */
export const PageSuspenseBoundary: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  return (
    <SuspenseBoundary
      fallback={
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="min-h-screen flex items-center justify-center"
        >
          <DefaultFallback />
        </motion.div>
      }
      className="min-h-screen"
    >
      {children}
    </SuspenseBoundary>
  );
};

/**
 * Suspense boundary for component sections
 */
export const SectionSuspenseBoundary: React.FC<{ 
  children: ReactNode;
  skeletonCount?: number;
}> = ({
  children,
  skeletonCount = 3,
}) => {
  return (
    <SuspenseBoundary
      fallback={
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {Array.from({ length: skeletonCount }).map((_, index) => (
            <CardSkeleton key={index} />
          ))}
        </div>
      }
    >
      {children}
    </SuspenseBoundary>
  );
};

export default SuspenseBoundary;

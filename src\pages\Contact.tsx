
import React, { useState } from "react";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button-gradient";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { SEOHead } from "@/components/SEOHead";
import { useConcurrentForm, useDeferredSearch } from "@/hooks/useConcurrentFeatures";
import { Mail, Phone, MapPin, Send, MessageSquare, User, Building } from "lucide-react";

const Contact = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: '',
    interest: ''
  });
  const { toast } = useToast();
  const navigate = useNavigate();
  const { isPending, updateFormWithTransition } = useConcurrentForm();
  const { deferredValue: deferredFormData } = useDeferredSearch(formData);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    updateFormWithTransition(() => {
      setFormData(prev => ({ ...prev, [name]: value }));
    });
  };

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    updateFormWithTransition(() => {
      setFormData(prev => ({ ...prev, interest: e.target.value }));
    });
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter your name.",
        variant: "destructive",
      });
      return false;
    }

    if (!formData.email.trim() || !/\S+@\S+\.\S+/.test(formData.email)) {
      toast({
        title: "Validation Error",
        description: "Please enter a valid email address.",
        variant: "destructive",
      });
      return false;
    }

    if (!formData.message.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter a message.",
        variant: "destructive",
      });
      return false;
    }

    if (!formData.interest) {
      toast({
        title: "Validation Error",
        description: "Please select your area of interest.",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call - in production, this would send to your backend
      await new Promise(resolve => setTimeout(resolve, 1500));

      console.log("Form submitted:", formData);

      toast({
        title: "Message received!",
        description: "We'll get back to you within 24 hours.",
      });

      // Reset form and redirect to thank you page
      setFormData({ name: '', email: '', message: '', interest: '' });
      navigate("/thanks");
    } catch (error) {
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const interestOptions = [
    "Catalyst - AI Platform",
    "Metamorphic Reactor - Multi-Agent Orchestration",
    "Vault 024 - Generative Art & NFTs",
    "Custom AI Solutions",
    "Consulting & Strategy",
    "Partnership Opportunities"
  ];

  return (
    <>
      <SEOHead
        title="Contact Us - Let's Build Something Amazing"
        description="Ready to redefine reality with AI? Contact Metamorphic Labs to discuss your project and explore how our AI systems can transform your vision into reality."
        keywords="Contact Metamorphic Labs, AI Consultation, Project Inquiry, Catalyst Platform, AI Development, Custom AI Solutions"
        url="https://metamorphiclabs.ai/contact"
      />
      <div className="min-h-screen bg-black py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            Let's Build Something <span className="text-gradient">Amazing</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Ready to redefine reality with AI? Let's discuss your project and explore how our systems can transform your vision into reality.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-12">
          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="lg:col-span-1"
          >
            <Card className="sticky top-8 bg-gradient-to-br from-slate-900 to-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <MessageSquare className="h-5 w-5 mr-2" />
                  Get In Touch
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.3 }}
                  className="flex items-center space-x-3"
                >
                  <Mail className="h-5 w-5 text-primary" />
                  <div>
                    <p className="font-medium text-white">Email</p>
                    <a
                      href="mailto:<EMAIL>"
                      className="text-gray-300 hover:text-gradient transition-all duration-300"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.4 }}
                  className="flex items-center space-x-3"
                >
                  <Building className="h-5 w-5 text-primary" />
                  <div>
                    <p className="font-medium text-white">Business Inquiries</p>
                    <a
                      href="mailto:<EMAIL>"
                      className="text-gray-300 hover:text-gradient transition-all duration-300"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.5 }}
                  className="flex items-center space-x-3"
                >
                  <MapPin className="h-5 w-5 text-primary" />
                  <div>
                    <p className="font-medium text-white">Location</p>
                    <p className="text-gray-300">Global • Remote-First</p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.6 }}
                  className="flex items-center space-x-3"
                >
                  <Send className="h-5 w-5 text-primary" />
                  <div>
                    <p className="font-medium text-white">Response Time</p>
                    <p className="text-gray-300">Within 24 hours</p>
                  </div>
                </motion.div>
              </CardContent>
            </Card>

            {/* Quick Links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
            >
              <Card className="mt-6 bg-gradient-primary border-0">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-white mb-4">Explore Our Systems</h3>
                  <div className="space-y-3">
                    <a
                      href="https://catalyst.metamorphiclabs.ai"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block text-white/90 hover:text-white transition-colors duration-300"
                    >
                      → Catalyst Platform
                    </a>
                    <a
                      href="https://vault024.metamorphiclabs.ai"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block text-gold hover:text-gold/80 transition-colors duration-300"
                    >
                      → Vault 024 Gallery
                    </a>
                    <span className="block text-white/60">
                      → Metamorphic Reactor (Coming Soon)
                    </span>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>

          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="lg:col-span-2"
          >
            <Card className="bg-gradient-to-br from-slate-900 to-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Tell Us About Your Project
                </CardTitle>
                <p className="text-gray-300">
                  Share your vision and let's explore how our AI systems can bring it to life.
                </p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="name" className="text-white">Full Name *</Label>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className="bg-black/50 border-slate-600 text-white placeholder:text-gray-400"
                        placeholder="Your full name"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="email" className="text-white">Email Address *</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="bg-black/50 border-slate-600 text-white placeholder:text-gray-400"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="interest" className="text-white">Area of Interest *</Label>
                    <select
                      id="interest"
                      name="interest"
                      value={formData.interest}
                      onChange={handleSelectChange}
                      className="w-full px-3 py-2 bg-black/50 border border-slate-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select your area of interest</option>
                      {interestOptions.map((option) => (
                        <option key={option} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="message" className="text-white">Message *</Label>
                    <Textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      className="bg-black/50 border-slate-600 text-white placeholder:text-gray-400 min-h-[120px]"
                      placeholder="Tell us about your project, goals, and how we can help..."
                      required
                    />
                  </div>

                  <div className="pt-6">
                    <Button
                      type="submit"
                      variant="gradient"
                      size="lg"
                      disabled={isSubmitting}
                      className="w-full"
                    >
                      {isSubmitting ? (
                        <>
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                            className="mr-2"
                          >
                            <Send className="h-5 w-5" />
                          </motion.div>
                          Sending Message...
                        </>
                      ) : (
                        <>
                          <Send className="mr-2 h-5 w-5" />
                          Send Message
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </motion.div>
        </div>
        </div>
      </div>
    </>
  );
};

export default Contact;

import { useTransition, useDeferredValue, useCallback, useMemo } from 'react';

/**
 * Custom hook that provides React 19 concurrent features
 * for improved performance and user experience
 */
export function useConcurrentNavigation() {
  const [isPending, startTransition] = useTransition();

  const navigateWithTransition = useCallback((callback: () => void) => {
    startTransition(() => {
      callback();
    });
  }, []);

  return {
    isPending,
    navigateWithTransition,
  };
}

/**
 * Hook for deferring expensive computations or searches
 */
export function useDeferredSearch<T>(value: T) {
  const deferredValue = useDeferredValue(value);
  
  return {
    deferredValue,
    isStale: value !== deferredValue,
  };
}

/**
 * Hook for managing concurrent updates in forms
 */
export function useConcurrentForm() {
  const [isPending, startTransition] = useTransition();

  const updateFormWithTransition = useCallback((updateFn: () => void) => {
    startTransition(() => {
      updateFn();
    });
  }, []);

  return {
    isPending,
    updateFormWithTransition,
  };
}

/**
 * Hook for managing concurrent data fetching
 */
export function useConcurrentData<T>(
  data: T,
  isLoading: boolean
) {
  const deferredData = useDeferredValue(data);
  const [isPending, startTransition] = useTransition();

  const refreshData = useCallback((refreshFn: () => void) => {
    startTransition(() => {
      refreshFn();
    });
  }, []);

  const isStale = useMemo(() => {
    return data !== deferredData;
  }, [data, deferredData]);

  return {
    data: deferredData,
    isLoading: isLoading || isPending,
    isStale,
    refreshData,
  };
}

/**
 * Hook for managing concurrent animations
 */
export function useConcurrentAnimation() {
  const [isPending, startTransition] = useTransition();

  const animateWithTransition = useCallback((animationFn: () => void) => {
    startTransition(() => {
      animationFn();
    });
  }, []);

  return {
    isPending,
    animateWithTransition,
  };
}

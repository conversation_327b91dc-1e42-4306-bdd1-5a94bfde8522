import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

interface AppState {
  // Theme state
  theme: 'dark' | 'light';
  setTheme: (theme: 'dark' | 'light') => void;
  
  // Navigation state
  isMobileMenuOpen: boolean;
  setMobileMenuOpen: (open: boolean) => void;
  
  // Contact form state
  contactFormData: {
    name: string;
    email: string;
    message: string;
    interest: string;
  };
  setContactFormData: (data: Partial<AppState['contactFormData']>) => void;
  resetContactForm: () => void;
  
  // Animation preferences
  prefersReducedMotion: boolean;
  setPrefersReducedMotion: (value: boolean) => void;
  
  // Loading states
  isLoading: boolean;
  setLoading: (loading: boolean) => void;
}

const initialContactFormData = {
  name: '',
  email: '',
  message: '',
  interest: '',
};

export const useStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        // Theme
        theme: 'dark', // Default to dark theme as per requirements
        setTheme: (theme) => set({ theme }),
        
        // Navigation
        isMobileMenuOpen: false,
        setMobileMenuOpen: (open) => set({ isMobileMenuOpen: open }),
        
        // Contact form
        contactFormData: initialContactFormData,
        setContactFormData: (data) =>
          set((state) => ({
            contactFormData: { ...state.contactFormData, ...data },
          })),
        resetContactForm: () => set({ contactFormData: initialContactFormData }),
        
        // Animation preferences
        prefersReducedMotion: false,
        setPrefersReducedMotion: (value) => set({ prefersReducedMotion: value }),
        
        // Loading
        isLoading: false,
        setLoading: (loading) => set({ isLoading: loading }),
      }),
      {
        name: 'metamorphic-labs-store',
        partialize: (state) => ({
          theme: state.theme,
          prefersReducedMotion: state.prefersReducedMotion,
        }),
      }
    ),
    {
      name: 'metamorphic-labs-store',
    }
  )
);

// Selectors for better performance - memoized to prevent infinite loops
export const useTheme = () => useStore((state) => state.theme);
export const useSetTheme = () => useStore((state) => state.setTheme);

// Memoized selectors to prevent object recreation on every render
const mobileMenuSelector = (state: AppState) => ({
  isOpen: state.isMobileMenuOpen,
  setOpen: state.setMobileMenuOpen,
});

const contactFormSelector = (state: AppState) => ({
  data: state.contactFormData,
  setData: state.setContactFormData,
  reset: state.resetContactForm,
});

const loadingSelector = (state: AppState) => ({
  isLoading: state.isLoading,
  setLoading: state.setLoading,
});

export const useMobileMenu = () => useStore(mobileMenuSelector);
export const useContactForm = () => useStore(contactFormSelector);
export const useLoading = () => useStore(loadingSelector);

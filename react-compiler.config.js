/**
 * React Compiler Configuration
 * 
 * This configuration enables React 19's experimental compiler features
 * for automatic optimizations and memoization.
 */

module.exports = {
  // Enable the React Compiler
  enabled: true,
  
  // Compilation target
  target: 'es2022',
  
  // Enable automatic memoization
  autoMemoization: true,
  
  // Enable automatic dependency tracking
  autoDependencies: true,
  
  // Optimization settings
  optimizations: {
    // Enable dead code elimination
    deadCodeElimination: true,
    
    // Enable constant folding
    constantFolding: true,
    
    // Enable inline functions
    inlineFunctions: true,
    
    // Enable component splitting
    componentSplitting: true,
  },
  
  // Source map generation
  sourceMaps: process.env.NODE_ENV === 'development',
  
  // Exclude patterns
  exclude: [
    'node_modules/**',
    '**/*.test.{js,jsx,ts,tsx}',
    '**/*.spec.{js,jsx,ts,tsx}',
    '**/test/**',
    '**/tests/**',
  ],
  
  // Include patterns
  include: [
    'src/**/*.{js,jsx,ts,tsx}',
  ],
  
  // Experimental features
  experimental: {
    // Enable React Server Components support
    serverComponents: false, // Will be enabled when we migrate to SSR
    
    // Enable automatic batching optimizations
    autoBatching: true,
    
    // Enable concurrent features optimizations
    concurrentFeatures: true,
    
    // Enable Suspense optimizations
    suspenseOptimizations: true,
  },
  
  // Development settings
  development: {
    // Enable hot reloading optimizations
    hotReload: true,
    
    // Enable development warnings
    warnings: true,
    
    // Enable performance profiling
    profiling: true,
  },
  
  // Production settings
  production: {
    // Enable aggressive optimizations
    aggressive: true,
    
    // Enable tree shaking
    treeShaking: true,
    
    // Enable minification
    minify: true,
    
    // Enable compression
    compress: true,
  },
};

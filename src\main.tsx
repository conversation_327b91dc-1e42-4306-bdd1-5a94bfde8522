import React from 'react'
import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'

const container = document.getElementById("root")!

// Enable React 19 concurrent features
const root = createRoot(container, {
  // Enable concurrent features
  unstable_strictMode: true,
  // Enable automatic batching for all updates
  unstable_concurrentUpdatesByDefault: true,
})

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
)

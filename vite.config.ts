import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// React Compiler plugin (experimental)
const reactCompilerPlugin = () => {
  return {
    name: 'react-compiler',
    transform(code: string, id: string) {
      // Only process React components
      if (id.includes('node_modules') || !id.match(/\.(jsx?|tsx?)$/)) {
        return null;
      }

      // Add React Compiler transformations here when available
      // This is a placeholder for future React Compiler integration
      return null;
    },
  };
};

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react({
      // Enable React 19 features
      jsxRuntime: 'automatic',
      jsxImportSource: 'react',
      // Enable development optimizations
      devTarget: 'es2022',
      // Enable React Compiler integration (experimental)
      plugins: mode === 'development' ? [] : [],
    }),
    mode === 'development' && componentTagger(),
    // Add React Compiler plugin (experimental)
    mode === 'production' && reactCompilerPlugin(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-select'],
          motion: ['framer-motion'],
          utils: ['class-variance-authority', 'clsx', 'tailwind-merge'],
        },
      },
    },
    chunkSizeWarningLimit: 1000,
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'framer-motion'],
  },
}));

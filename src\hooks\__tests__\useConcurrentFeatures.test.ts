import { renderHook, act } from '@testing-library/react';
import {
  useConcurrentNavigation,
  useDeferredSearch,
  useConcurrentForm,
  useConcurrentData,
  useConcurrentAnimation,
} from '../useConcurrentFeatures';
import { waitForConcurrentUpdate, waitForTransition } from '../../test/setup';

describe('useConcurrentNavigation', () => {
  it('should handle navigation with transitions', async () => {
    const mockCallback = jest.fn();
    const { result } = renderHook(() => useConcurrentNavigation());

    expect(result.current.isPending).toBe(false);

    act(() => {
      result.current.navigateWithTransition(mockCallback);
    });

    // In test environment, transitions may complete immediately
    // so we check that the callback was called
    expect(mockCallback).toHaveBeenCalled();

    // Wait for any pending updates
    await waitForTransition();

    // After transition, pending should be false
    expect(result.current.isPending).toBe(false);
  });
});

describe('useDeferredSearch', () => {
  it('should defer value updates', async () => {
    const { result, rerender } = renderHook(
      ({ value }) => useDeferredSearch(value),
      { initialProps: { value: 'initial' } }
    );

    expect(result.current.deferredValue).toBe('initial');
    expect(result.current.isStale).toBe(false);

    // Update the value
    rerender({ value: 'updated' });

    // In test environment, deferred values may update immediately
    // Check that we have the updated value and proper stale state
    await waitForConcurrentUpdate();

    expect(result.current.deferredValue).toBe('updated');
    expect(result.current.isStale).toBe(false);
  });
});

describe('useConcurrentForm', () => {
  it('should handle form updates with transitions', async () => {
    const { result } = renderHook(() => useConcurrentForm());
    const mockUpdateFn = jest.fn();

    expect(result.current.isPending).toBe(false);

    act(() => {
      result.current.updateFormWithTransition(mockUpdateFn);
    });

    // Check that the callback was called
    expect(mockUpdateFn).toHaveBeenCalled();

    await waitForTransition();

    expect(result.current.isPending).toBe(false);
  });
});

describe('useConcurrentData', () => {
  it('should handle data with deferred values and refresh', async () => {
    const mockRefreshFn = jest.fn();
    const { result, rerender } = renderHook(
      ({ data, isLoading }) => useConcurrentData(data, isLoading),
      { initialProps: { data: 'initial', isLoading: false } }
    );

    expect(result.current.data).toBe('initial');
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isStale).toBe(false);

    // Update data
    rerender({ data: 'updated', isLoading: false });

    // Wait for deferred update
    await waitForConcurrentUpdate();

    // Test refresh functionality
    act(() => {
      result.current.refreshData(mockRefreshFn);
    });

    expect(mockRefreshFn).toHaveBeenCalled();

    await waitForTransition();

    expect(result.current.isLoading).toBe(false);
  });
});

describe('useConcurrentAnimation', () => {
  it('should handle animations with transitions', async () => {
    const { result } = renderHook(() => useConcurrentAnimation());
    const mockAnimationFn = jest.fn();

    expect(result.current.isPending).toBe(false);

    act(() => {
      result.current.animateWithTransition(mockAnimationFn);
    });

    expect(mockAnimationFn).toHaveBeenCalled();

    await waitForTransition();

    expect(result.current.isPending).toBe(false);
  });
});

describe('Concurrent Features Integration', () => {
  it('should work together without conflicts', async () => {
    const navCallback = jest.fn();
    const formCallback = jest.fn();
    const animCallback = jest.fn();

    const { result: navResult } = renderHook(() => useConcurrentNavigation());
    const { result: formResult } = renderHook(() => useConcurrentForm());
    const { result: animResult } = renderHook(() => useConcurrentAnimation());

    // All should start as not pending
    expect(navResult.current.isPending).toBe(false);
    expect(formResult.current.isPending).toBe(false);
    expect(animResult.current.isPending).toBe(false);

    // Trigger multiple concurrent operations
    act(() => {
      navResult.current.navigateWithTransition(navCallback);
      formResult.current.updateFormWithTransition(formCallback);
      animResult.current.animateWithTransition(animCallback);
    });

    // All callbacks should be called
    expect(navCallback).toHaveBeenCalled();
    expect(formCallback).toHaveBeenCalled();
    expect(animCallback).toHaveBeenCalled();

    await waitForTransition();

    // All should complete
    expect(navResult.current.isPending).toBe(false);
    expect(formResult.current.isPending).toBe(false);
    expect(animResult.current.isPending).toBe(false);
  });
});

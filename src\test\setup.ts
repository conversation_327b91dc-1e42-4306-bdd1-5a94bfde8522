import '@testing-library/jest-dom';

// React 19 concurrent features testing setup
import { act } from '@testing-library/react';

// Mock Z<PERSON>and store to prevent infinite loops in tests
jest.mock('../store/useStore', () => {
  const mockStore = {
    theme: 'dark',
    isMobileMenuOpen: false,
    isLoading: false,
    prefersReducedMotion: false,
    contactFormData: {
      name: '',
      email: '',
      message: '',
      interest: '',
    },
    setTheme: jest.fn(),
    setMobileMenuOpen: jest.fn(),
    setContactFormData: jest.fn(),
    resetContactForm: jest.fn(),
    setPrefersReducedMotion: jest.fn(),
    setLoading: jest.fn(),
  };

  return {
    useStore: jest.fn(() => mockStore),
    useTheme: jest.fn(() => mockStore.theme),
    useSetTheme: jest.fn(() => mockStore.setTheme),
    useMobileMenu: jest.fn(() => ({
      isOpen: mockStore.isMobileMenuOpen,
      setOpen: mockStore.setMobileMenuOpen,
    })),
    useContactForm: jest.fn(() => ({
      data: mockStore.contactFormData,
      setData: mockStore.setContactFormData,
      reset: mockStore.resetContactForm,
    })),
    useLoading: jest.fn(() => ({
      isLoading: mockStore.isLoading,
      setLoading: mockStore.setLoading,
    })),
  };
});

// Enable React 19 concurrent features in tests
beforeEach(() => {
  // Mock scheduler for concurrent features
  global.scheduler = {
    postTask: jest.fn((callback) => {
      return Promise.resolve().then(callback);
    }),
    unstable_scheduleCallback: jest.fn((priority, callback) => {
      return setTimeout(callback, 0);
    }),
    unstable_cancelCallback: jest.fn(),
  };
});

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock PerformanceObserver for performance monitoring tests
interface MockPerformanceEntry {
  entryType: string;
  startTime: number;
  name?: string;
}

interface MockPerformanceObserverEntryList {
  getEntries(): MockPerformanceEntry[];
}

type PerformanceObserverCallback = (list: MockPerformanceObserverEntryList) => void;

global.PerformanceObserver = class PerformanceObserver {
  constructor(callback: PerformanceObserverCallback) {
    this.callback = callback;
  }
  callback: PerformanceObserverCallback;
  observe() {}
  disconnect() {}
};

// Mock performance.now for consistent timing in tests
const originalPerformanceNow = performance.now;
beforeEach(() => {
  let mockTime = 0;
  performance.now = jest.fn(() => mockTime++);
});

afterEach(() => {
  performance.now = originalPerformanceNow;
});

// Helper for testing concurrent features
export const waitForConcurrentUpdate = async () => {
  await act(async () => {
    await new Promise(resolve => setTimeout(resolve, 0));
  });
};

// Helper for testing transitions
export const waitForTransition = async () => {
  await act(async () => {
    await new Promise(resolve => setTimeout(resolve, 100));
  });
};
